<template>
    <div class="home all-padding-12 border-box" style="background-color: #f4fbff">
        <!-- 线索相关列表以及风险监控 -->
        <div class="display-flex flex-column gap-8">
            <div class="display-flex space-between all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <LeadMenus />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-20 color-black font-weight-600 b-margin-24">我的代办</span>
                <MyAgent />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-20 color-black font-weight-600 b-margin-24">企业风险监控</span>
                <companyRiskMonitor />
            </div>
            <div class="all-padding-24 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-20 color-black font-weight-600 b-margin-24">销售漏斗</span>
                <div class="w-100" style="height: 300px; margin-top: rem;">
                    <v-chart :option="funnelOption" style="height: 100%; width: 100%;" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { onMounted, computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { FunnelChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import LeadMenus from './components/LeadMenus.vue'
import MyAgent from './components/MyAgent.vue'
import companyRiskMonitor from './components/companyRiskMonitor.vue'

// 注册必要的组件
use([
    CanvasRenderer,
    FunnelChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent
])

// ECharts 漏斗图配置
const funnelOption = computed(() => {
    return {
        color: ['#1966FF', '#60BCFF', '#18B4AB'],
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 10,
            textStyle: {
                fontSize: 12
            }
        },
        series: [
            {
                name: '销售漏斗',
                type: 'funnel',
                left: '10%',
                top: 60,
                width: '80%',
                height: '60%',
                minSize: '20%',
                maxSize: '100%',
                sort: 'descending',
                gap: 2,
                label: {
                    show: true,
                    position: 'inside',
                    formatter: '{c}',
                    fontSize: 12
                },
                labelLine: {
                    length: 10,
                    lineStyle: {
                        width: 1,
                        type: 'solid'
                    }
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1
                },
                emphasis: {
                    label: {
                        fontSize: 14
                    }
                },
                data: [
                    { value: 123, name: '客户' },
                    { value: 89, name: '线索' },
                    { value: 23, name: '成交客户' }
                ]
            }
        ]
    }
})

const init = () => {}

onMounted(() => {
    init()
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    display: flex;
    flex-direction: column;
}
</style>